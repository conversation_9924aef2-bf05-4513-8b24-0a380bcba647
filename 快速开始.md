# 快速开始指南

## 第一步：准备密钥

1. 编辑 `示例密钥.txt` 文件，将示例密钥替换为您的真实API密钥
2. 或者准备一个包含API密钥的文本文件（每行一个密钥）

## 第二步：运行程序

```bash
python api_key_manager.py
```

## 第三步：添加密钥

1. 选择选项 `2` (添加密钥)
2. 逐行粘贴您的API密钥
3. 输入空行完成添加

## 第四步：提取密钥

1. 选择选项 `1` (提取密钥)
2. 输入要提取的数量，例如：`3`
3. 系统会显示3个可用的密钥
4. **重要：这些密钥提取后就不能再次使用了**

## 示例操作流程

```
=== API密钥提取管理系统 ===
可用密钥数量: 10
已使用密钥数量: 5
最后更新时间: 2025-08-03T11:27:40

请选择操作：
1. 提取密钥
2. 添加密钥  
3. 查看可用密钥
4. 清空已使用密钥记录
5. 退出

请输入选项 (1-5): 1
请输入要提取的密钥数量: 2

成功提取 2 个密钥：
1. sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
2. api-key-yyyyyyyyyyyyyyyyyyyyyyyyyyyy
```

## 注意事项

- ✅ 提取的密钥会自动标记为已使用
- ✅ 已使用的密钥不会再次被提取
- ✅ 系统会自动保存所有操作记录
- ⚠️ 请妥善保管 `api_keys.json` 文件
- ⚠️ 建议定期备份密钥数据

## 常用命令

- **查看状态**：程序启动时自动显示
- **批量添加**：选项2，支持一次添加多个密钥
- **安全查看**：选项3，默认只显示密钥前8位
- **清理记录**：选项4，清空已使用密钥的历史记录
