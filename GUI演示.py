#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI演示脚本 - 展示API密钥管理系统的图形界面功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import sys
import os

def check_requirements():
    """检查运行要求"""
    try:
        import tkinter
        print("✅ tkinter 可用")
    except ImportError:
        print("❌ tkinter 不可用")
        return False
    
    if os.path.exists('api_key_manager.py'):
        print("✅ 核心管理模块存在")
    else:
        print("❌ 缺少 api_key_manager.py")
        return False
    
    if os.path.exists('api_key_gui.py'):
        print("✅ GUI程序存在")
    else:
        print("❌ 缺少 api_key_gui.py")
        return False
    
    return True

def create_demo_window():
    """创建演示窗口"""
    root = tk.Tk()
    root.title("API密钥管理系统 - 演示启动器")
    root.geometry("600x400")
    root.resizable(False, False)
    
    # 主框架
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="API密钥管理系统", 
                           font=('Arial', 18, 'bold'))
    title_label.pack(pady=(0, 20))
    
    # 说明文本
    info_text = """
🔑 功能特点：
• 图形化界面，操作简单直观
• 输入数量，一键提取密钥
• 提取后自动标记为已使用
• 支持批量添加和文件导入
• 实时状态显示和历史记录

📋 界面布局：
• 顶部：系统状态（可用/已使用密钥数量）
• 左侧：密钥提取区域
• 右侧：密钥添加区域  
• 中间：操作结果显示
• 底部：功能按钮（查看、清理、导入等）

⚡ 使用流程：
1. 点击下方按钮启动GUI程序
2. 添加您的API密钥
3. 输入数量提取密钥
4. 复制使用提取的密钥
    """
    
    info_label = ttk.Label(main_frame, text=info_text, 
                          font=('Arial', 10), justify=tk.LEFT)
    info_label.pack(pady=(0, 20))
    
    # 按钮框架
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(pady=20)
    
    def launch_gui():
        """启动GUI程序"""
        try:
            subprocess.Popen([sys.executable, 'api_key_gui.py'])
            messagebox.showinfo("启动成功", "GUI程序已启动！\n请查看新打开的窗口。")
        except Exception as e:
            messagebox.showerror("启动失败", f"无法启动GUI程序：{str(e)}")
    
    def launch_console():
        """启动控制台版本"""
        try:
            subprocess.Popen([sys.executable, 'api_key_manager.py'])
            messagebox.showinfo("启动成功", "控制台程序已启动！\n请查看命令行窗口。")
        except Exception as e:
            messagebox.showerror("启动失败", f"无法启动控制台程序：{str(e)}")
    
    def show_help():
        """显示帮助信息"""
        help_window = tk.Toplevel(root)
        help_window.title("使用帮助")
        help_window.geometry("500x300")
        help_window.resizable(False, False)
        
        help_text = """
📖 快速上手指南

1. 启动程序
   • 点击"启动GUI版本"打开图形界面
   • 或点击"启动控制台版本"使用命令行

2. 添加密钥
   • 在右侧文本框输入密钥（每行一个）
   • 或点击"导入文件"从文件批量导入
   • 点击"添加密钥"按钮确认

3. 提取密钥
   • 在左侧输入要提取的数量
   • 点击"提取密钥"按钮
   • 在结果区查看提取的密钥

4. 管理功能
   • "查看可用密钥"：查看当前密钥列表
   • "清空已使用记录"：清理历史记录
   • "刷新状态"：更新显示信息

⚠️ 重要提醒
• 提取的密钥会被标记为已使用
• 已使用的密钥不能再次提取
• 所有数据自动保存到本地文件
        """
        
        help_label = ttk.Label(help_window, text=help_text, 
                              font=('Arial', 9), justify=tk.LEFT)
        help_label.pack(padx=20, pady=20)
    
    # 按钮
    ttk.Button(button_frame, text="🖥️ 启动GUI版本", 
              command=launch_gui, width=20).pack(side=tk.LEFT, padx=5)
    
    ttk.Button(button_frame, text="💻 启动控制台版本", 
              command=launch_console, width=20).pack(side=tk.LEFT, padx=5)
    
    ttk.Button(button_frame, text="❓ 使用帮助", 
              command=show_help, width=15).pack(side=tk.LEFT, padx=5)
    
    # 状态信息
    status_frame = ttk.Frame(main_frame)
    status_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(20, 0))
    
    # 检查文件状态
    status_text = "系统状态：\n"
    if os.path.exists('api_keys.json'):
        try:
            import json
            with open('api_keys.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            available = len(data.get('keys', []))
            used = len(data.get('used_keys', []))
            status_text += f"• 可用密钥：{available} 个\n• 已使用密钥：{used} 个"
        except:
            status_text += "• 数据文件存在但无法读取"
    else:
        status_text += "• 暂无数据文件（首次运行后自动创建）"
    
    status_label = ttk.Label(status_frame, text=status_text, 
                            font=('Arial', 9), foreground='gray')
    status_label.pack()
    
    return root

def main():
    """主函数"""
    print("API密钥管理系统 - 演示启动器")
    print("=" * 40)
    
    # 检查运行要求
    if not check_requirements():
        print("\n❌ 系统检查失败，请确保所有必需文件存在")
        input("按回车键退出...")
        return
    
    print("\n✅ 系统检查通过")
    print("\n正在启动演示界面...")
    
    # 创建并运行演示窗口
    try:
        root = create_demo_window()
        root.mainloop()
    except Exception as e:
        print(f"\n❌ 启动演示界面失败：{str(e)}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
