# API密钥提取管理系统

## 功能介绍

这是一个API密钥管理系统，主要功能包括：

- **密钥提取**：输入要提取的数量，系统会提供指定数量的未使用密钥
- **一次性使用**：提取后的密钥会被标记为已使用，不能再次提取
- **密钥管理**：可以添加新密钥、查看状态、清理记录等

## 文件说明

- `api_key_manager.py` - 主程序文件
- `api_keys.json` - 密钥数据存储文件（运行后自动生成）
- `使用说明.md` - 本说明文档

## 安装要求

- Python 3.6 或更高版本
- 无需额外依赖包（使用Python标准库）

## 使用方法

### 1. 运行程序

```bash
python api_key_manager.py
```

### 2. 主要操作

#### 添加密钥
1. 选择选项 `2`
2. 逐行输入要添加的API密钥
3. 输入空行结束添加

#### 提取密钥
1. 选择选项 `1`
2. 输入要提取的密钥数量
3. 系统会显示提取的密钥列表
4. **注意：提取后的密钥会被标记为已使用，不能再次提取**

#### 查看状态
- 选择选项 `3` 查看可用密钥列表
- 程序启动时会自动显示当前状态

#### 清理记录
- 选择选项 `4` 清空已使用密钥的记录（不影响可用密钥）

## 数据存储格式

系统使用JSON格式存储数据：

```json
{
  "keys": [
    {
      "id": "唯一标识符",
      "key": "API密钥内容",
      "added_at": "添加时间",
      "status": "available"
    }
  ],
  "used_keys": [
    {
      "id": "唯一标识符",
      "key": "API密钥内容",
      "added_at": "添加时间",
      "used_at": "使用时间",
      "status": "used"
    }
  ],
  "created_at": "创建时间",
  "last_updated": "最后更新时间"
}
```

## 安全注意事项

1. **文件权限**：确保 `api_keys.json` 文件的访问权限设置合适
2. **备份**：定期备份密钥数据文件
3. **环境安全**：在安全的环境中运行此程序
4. **密钥保护**：避免在不安全的环境中显示完整密钥

## 示例使用流程

1. **初次使用**：
   ```
   运行程序 → 添加密钥 → 查看状态
   ```

2. **日常提取**：
   ```
   运行程序 → 提取密钥 → 复制使用
   ```

3. **维护管理**：
   ```
   查看状态 → 添加新密钥 → 清理记录
   ```

## 常见问题

**Q: 提取的密钥能否恢复为可用状态？**
A: 不能。这是系统的设计特性，确保密钥的一次性使用。

**Q: 如何批量添加密钥？**
A: 在添加密钥界面，每行输入一个密钥，可以连续输入多个。

**Q: 数据文件损坏怎么办？**
A: 程序会自动创建新的数据文件，但之前的密钥数据会丢失。建议定期备份。

**Q: 可以修改密钥存储文件的位置吗？**
A: 可以修改程序中的 `config_file` 参数来指定不同的文件路径。

## 技术支持

如有问题或建议，请检查：
1. Python版本是否符合要求
2. 文件权限是否正确
3. 数据文件是否完整
