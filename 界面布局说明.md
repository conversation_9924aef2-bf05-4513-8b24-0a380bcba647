# API密钥管理系统 - 界面布局说明

## 🖼️ 主界面布局

```
┌─────────────────────────────────────────────────────────────────────────┐
│                    API密钥提取管理系统                                      │
├─────────────────────────────────────────────────────────────────────────┤
│  系统状态                                                                │
│  ┌─────────────────────────────────────────────────────────────────────┐ │
│  │ 可用密钥: 15        已使用密钥: 8                                    │ │
│  │ 最后更新: 2025-08-03 11:30:25                                      │ │
│  └─────────────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────────────┐  ┌─────────────────────────────────────────────┐ │
│  │    提取密钥         │  │           添加密钥                          │ │
│  │                     │  │                                             │ │
│  │ 提取数量: [____3____]│  │ 密钥列表 (每行一个):                        │ │
│  │                     │  │ ┌─────────────────────────────────────────┐ │ │
│  │ [   提取密钥   ]    │  │ │sk-1234567890abcdef...                   │ │ │
│  │                     │  │ │api-key-example-111...                   │ │ │
│  └─────────────────────┘  │ │sk-9876543210zyxwvu...                   │ │ │
│                            │ │                                         │ │ │
│                            │ └─────────────────────────────────────────┘ │ │
│                            │                                             │ │
│                            │ [      添加密钥      ]                      │ │
│                            └─────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────┤
│  操作结果                                                                │
│  ┌─────────────────────────────────────────────────────────────────────┐ │
│  │ 成功提取 3 个密钥:                                                   │ │
│  │                                                                     │ │
│  │ 1. sk-1234567890abcdefghijklmnopqrstuvwxyz                          │ │
│  │ 2. api-key-example-111111111111111111111111111                     │ │
│  │ 3. sk-9876543210zyxwvutsrqponmlkjihgfedcba                          │ │
│  │                                                                     │ │
│  └─────────────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────┤
│ [查看可用密钥] [清空已使用记录] [刷新状态] [清空结果] [导入文件]           │
└─────────────────────────────────────────────────────────────────────────┘
```

## 📱 界面区域详解

### 1. 标题栏
- **位置**: 窗口顶部
- **内容**: "API密钥提取管理系统"
- **样式**: 大号粗体字体

### 2. 系统状态区
- **位置**: 标题下方
- **显示内容**:
  - 可用密钥数量
  - 已使用密钥数量  
  - 最后更新时间
- **特点**: 实时更新，自动刷新

### 3. 提取密钥区（左侧）
- **输入框**: 输入要提取的密钥数量
- **按钮**: "提取密钥" - 执行提取操作
- **验证**: 自动检查输入有效性

### 4. 添加密钥区（右侧）
- **文本框**: 多行输入，支持滚动
- **格式**: 每行一个密钥
- **按钮**: "添加密钥" - 执行添加操作
- **过滤**: 自动忽略空行和注释

### 5. 操作结果区（中央）
- **显示内容**: 
  - 提取的完整密钥列表
  - 添加操作的结果统计
  - 错误信息和警告
- **特点**: 支持滚动，可复制内容

### 6. 功能按钮区（底部）
- **查看可用密钥**: 显示当前密钥预览
- **清空已使用记录**: 清理历史数据
- **刷新状态**: 手动更新状态显示
- **清空结果**: 清除结果显示区
- **导入文件**: 从文件批量导入密钥

## 🎨 界面特色

### 颜色方案
- **主色调**: 现代灰蓝色系
- **强调色**: 蓝色按钮
- **状态色**: 绿色(成功)、红色(错误)、橙色(警告)

### 字体设置
- **标题**: Arial 16pt 粗体
- **正文**: Arial 10pt 常规
- **按钮**: Arial 10pt 粗体

### 布局特点
- **响应式**: 支持窗口大小调整
- **网格布局**: 整齐对齐，比例协调
- **间距合理**: 适当的内边距和外边距

## 🔧 交互设计

### 输入验证
- 提取数量必须为正整数
- 密钥不能为空
- 自动过滤无效输入

### 用户反馈
- 操作成功/失败的弹窗提示
- 实时状态更新
- 详细的结果显示

### 快捷操作
- 回车键确认输入
- Tab键切换焦点
- 支持复制粘贴

## 📏 窗口规格

- **默认大小**: 800x600 像素
- **最小大小**: 600x400 像素
- **可调整**: 支持拖拽调整大小
- **比例**: 保持界面元素协调

## 🎯 使用体验

### 新手友好
- 直观的界面布局
- 清晰的功能分区
- 详细的操作提示

### 高效操作
- 一键提取密钥
- 批量添加支持
- 快速状态查看

### 安全考虑
- 密钥预览时隐藏敏感部分
- 本地数据存储
- 操作日志记录

## 🚀 启动方式

### 方法1: 直接启动GUI
```bash
python api_key_gui.py
```

### 方法2: 通过演示启动器
```bash
python GUI演示.py
```

### 方法3: 双击运行
- 在文件管理器中双击 `api_key_gui.py`
- 或双击 `GUI演示.py` 选择启动方式
