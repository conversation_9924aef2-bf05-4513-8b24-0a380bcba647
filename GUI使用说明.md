# API密钥管理系统 - GUI版本使用说明

## 🖥️ 界面概览

这是一个基于Python tkinter的桌面应用程序，提供友好的图形界面来管理API密钥。

## 🚀 启动程序

```bash
python api_key_gui.py
```

## 📋 界面布局

### 1. 顶部标题区
- 显示"API密钥提取管理系统"标题

### 2. 系统状态区
- **可用密钥**: 显示当前可用的密钥数量
- **已使用密钥**: 显示已经提取使用的密钥数量  
- **最后更新**: 显示最后一次操作的时间

### 3. 主要操作区

#### 左侧 - 提取密钥
- **提取数量输入框**: 输入要提取的密钥数量
- **提取密钥按钮**: 点击执行提取操作

#### 右侧 - 添加密钥
- **密钥列表文本框**: 输入要添加的密钥（每行一个）
- **添加密钥按钮**: 点击执行添加操作

### 4. 结果显示区
- 显示所有操作的结果和提取的密钥
- 支持滚动查看长内容

### 5. 底部功能按钮
- **查看可用密钥**: 显示当前所有可用密钥的预览
- **清空已使用记录**: 清除已使用密钥的历史记录
- **刷新状态**: 手动刷新状态显示
- **清空结果**: 清空结果显示区的内容
- **导入文件**: 从文本文件批量导入密钥

## 🔧 主要功能

### 提取密钥
1. 在"提取数量"输入框中输入数字（如：3）
2. 点击"提取密钥"按钮
3. 系统会在结果区显示提取的密钥
4. 提取的密钥会自动标记为已使用

### 添加密钥
1. 在右侧文本框中输入密钥，每行一个
2. 点击"添加密钥"按钮
3. 系统会显示添加结果
4. 重复的密钥会被自动跳过

### 导入文件
1. 点击"导入文件"按钮
2. 选择包含密钥的文本文件
3. 系统会自动读取并添加密钥
4. 支持过滤注释行（以#开头的行）

### 查看状态
- 程序会实时更新状态信息
- 可以点击"刷新状态"手动更新
- 查看可用密钥时只显示前12位+省略号

## 💡 使用技巧

### 批量添加密钥
```
sk-1234567890abcdefghijklmnopqrstuvwxyz
api-key-example-111111111111111111111111
# 这是注释，会被忽略
sk-9876543210zyxwvutsrqponmlkjihgfedcba
```

### 文件导入格式
创建一个`.txt`文件，每行一个密钥：
```
sk-test1234567890abcdefghijklmnopqrstuvwxyz
sk-demo9876543210zyxwvutsrqponmlkjihgfedcba
api-key-example-111111111111111111111111111
```

### 安全提示
- 密钥在"查看可用密钥"时只显示前12位
- 完整密钥只在提取时显示
- 所有数据保存在本地JSON文件中

## ⚠️ 注意事项

1. **一次性使用**: 提取的密钥会被标记为已使用，不能再次提取
2. **数据持久化**: 所有操作会自动保存到`api_keys.json`文件
3. **重复检查**: 添加密钥时会自动检查重复，跳过已存在的密钥
4. **文件权限**: 确保程序有读写当前目录的权限

## 🔄 操作流程示例

### 首次使用
1. 启动程序 → 查看初始状态（0个可用密钥）
2. 点击"添加密钥" → 输入密钥列表 → 确认添加
3. 查看更新后的状态
4. 点击"提取密钥" → 输入数量 → 获取密钥

### 日常使用
1. 启动程序 → 查看当前状态
2. 根据需要提取密钥
3. 复制使用提取的密钥

### 维护管理
1. 定期添加新密钥
2. 查看可用密钥数量
3. 清理已使用记录（可选）

## 🛠️ 技术要求

- Python 3.6+
- tkinter（Python标准库）
- 无需额外安装依赖

## 📁 相关文件

- `api_key_gui.py` - GUI主程序
- `api_key_manager.py` - 核心管理逻辑
- `api_keys.json` - 数据存储文件（自动生成）
- `示例密钥.txt` - 示例密钥文件

## 🆘 常见问题

**Q: 程序无法启动？**
A: 检查Python版本，确保安装了tkinter

**Q: 提取的密钥能恢复吗？**
A: 不能，这是设计特性，确保一次性使用

**Q: 如何备份数据？**
A: 复制`api_keys.json`文件即可

**Q: 界面显示异常？**
A: 尝试调整窗口大小，或重启程序
