#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API密钥提取管理系统
功能：输入提取数量，提取指定数量的未使用密钥，提取后标记为已使用
"""

import json
import os
import uuid
from datetime import datetime
from typing import List, Dict, Optional

class APIKeyManager:
    def __init__(self, config_file: str = "api_keys.json"):
        """
        初始化API密钥管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.keys_data = self._load_keys()
    
    def _load_keys(self) -> Dict:
        """加载密钥数据"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                pass
        
        # 如果文件不存在或损坏，创建默认结构
        return {
            "keys": [],
            "used_keys": [],
            "created_at": datetime.now().isoformat(),
            "last_updated": datetime.now().isoformat()
        }
    
    def _save_keys(self):
        """保存密钥数据到文件"""
        self.keys_data["last_updated"] = datetime.now().isoformat()
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.keys_data, f, ensure_ascii=False, indent=2)
    
    def add_keys(self, keys: List[str]) -> int:
        """
        添加新的API密钥到池中
        
        Args:
            keys: 要添加的密钥列表
            
        Returns:
            成功添加的密钥数量
        """
        added_count = 0
        existing_keys = {key["key"] for key in self.keys_data["keys"]}
        existing_used_keys = {key["key"] for key in self.keys_data["used_keys"]}
        
        for key in keys:
            if key and key not in existing_keys and key not in existing_used_keys:
                key_info = {
                    "id": str(uuid.uuid4()),
                    "key": key,
                    "added_at": datetime.now().isoformat(),
                    "status": "available"
                }
                self.keys_data["keys"].append(key_info)
                added_count += 1
        
        if added_count > 0:
            self._save_keys()
        
        return added_count
    
    def get_available_count(self) -> int:
        """获取可用密钥数量"""
        return len(self.keys_data["keys"])
    
    def get_used_count(self) -> int:
        """获取已使用密钥数量"""
        return len(self.keys_data["used_keys"])
    
    def extract_keys(self, count: int) -> List[str]:
        """
        提取指定数量的密钥
        
        Args:
            count: 要提取的密钥数量
            
        Returns:
            提取的密钥列表
        """
        if count <= 0:
            return []
        
        available_keys = self.keys_data["keys"]
        if count > len(available_keys):
            print(f"警告：请求提取 {count} 个密钥，但只有 {len(available_keys)} 个可用密钥")
            count = len(available_keys)
        
        # 提取前count个密钥
        extracted_keys = available_keys[:count]
        extracted_key_strings = [key["key"] for key in extracted_keys]
        
        # 将提取的密钥移动到已使用列表
        for key_info in extracted_keys:
            key_info["used_at"] = datetime.now().isoformat()
            key_info["status"] = "used"
            self.keys_data["used_keys"].append(key_info)
        
        # 从可用列表中移除
        self.keys_data["keys"] = available_keys[count:]
        
        # 保存更改
        self._save_keys()
        
        return extracted_key_strings
    
    def show_status(self):
        """显示当前状态"""
        print(f"\n=== API密钥管理系统状态 ===")
        print(f"可用密钥数量: {self.get_available_count()}")
        print(f"已使用密钥数量: {self.get_used_count()}")
        print(f"最后更新时间: {self.keys_data.get('last_updated', '未知')}")
    
    def list_available_keys(self, show_keys: bool = False):
        """
        列出可用密钥
        
        Args:
            show_keys: 是否显示完整密钥（默认只显示前8位）
        """
        print(f"\n=== 可用密钥列表 ({len(self.keys_data['keys'])}个) ===")
        for i, key_info in enumerate(self.keys_data["keys"], 1):
            key_display = key_info["key"] if show_keys else key_info["key"][:8] + "..."
            print(f"{i}. {key_display} (添加时间: {key_info['added_at'][:19]})")
    
    def clear_used_keys(self) -> int:
        """
        清空已使用的密钥记录
        
        Returns:
            清空的密钥数量
        """
        count = len(self.keys_data["used_keys"])
        self.keys_data["used_keys"] = []
        self._save_keys()
        return count


def main():
    """主函数 - 命令行界面"""
    manager = APIKeyManager()
    
    while True:
        print("\n" + "="*50)
        print("API密钥提取管理系统")
        print("="*50)
        manager.show_status()
        print("\n请选择操作：")
        print("1. 提取密钥")
        print("2. 添加密钥")
        print("3. 查看可用密钥")
        print("4. 清空已使用密钥记录")
        print("5. 退出")
        
        choice = input("\n请输入选项 (1-5): ").strip()
        
        if choice == "1":
            try:
                count = int(input("请输入要提取的密钥数量: "))
                if count <= 0:
                    print("提取数量必须大于0")
                    continue
                
                extracted = manager.extract_keys(count)
                if extracted:
                    print(f"\n成功提取 {len(extracted)} 个密钥：")
                    for i, key in enumerate(extracted, 1):
                        print(f"{i}. {key}")
                else:
                    print("没有可用的密钥")
            except ValueError:
                print("请输入有效的数字")
        
        elif choice == "2":
            print("请输入要添加的密钥（每行一个，输入空行结束）：")
            keys_to_add = []
            while True:
                key = input().strip()
                if not key:
                    break
                keys_to_add.append(key)
            
            if keys_to_add:
                added = manager.add_keys(keys_to_add)
                print(f"成功添加 {added} 个新密钥")
            else:
                print("没有添加任何密钥")
        
        elif choice == "3":
            show_full = input("是否显示完整密钥？(y/N): ").strip().lower() == 'y'
            manager.list_available_keys(show_full)
        
        elif choice == "4":
            cleared = manager.clear_used_keys()
            print(f"已清空 {cleared} 个已使用密钥的记录")
        
        elif choice == "5":
            print("感谢使用API密钥管理系统！")
            break
        
        else:
            print("无效选项，请重新选择")


if __name__ == "__main__":
    main()
