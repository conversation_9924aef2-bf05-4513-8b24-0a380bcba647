#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API密钥管理系统 - Web界面版本
基于Flask的Web应用程序
"""

from flask import Flask, render_template, request, jsonify, flash, redirect, url_for
import json
import os
from datetime import datetime
from api_key_manager import APIKeyManager

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'  # 在生产环境中请更改此密钥

# 全局管理器实例
manager = APIKeyManager()

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/status')
def get_status():
    """获取系统状态"""
    return jsonify({
        'available_count': manager.get_available_count(),
        'used_count': manager.get_used_count(),
        'last_updated': manager.keys_data.get('last_updated', '未知')
    })

@app.route('/api/extract', methods=['POST'])
def extract_keys():
    """提取密钥"""
    try:
        data = request.get_json()
        count = int(data.get('count', 0))
        
        if count <= 0:
            return jsonify({'success': False, 'message': '提取数量必须大于0'})
        
        available = manager.get_available_count()
        if available == 0:
            return jsonify({'success': False, 'message': '没有可用的密钥'})
        
        extracted = manager.extract_keys(count)
        
        return jsonify({
            'success': True,
            'message': f'成功提取 {len(extracted)} 个密钥',
            'keys': extracted,
            'count': len(extracted)
        })
    
    except Exception as e:
        return jsonify({'success': False, 'message': f'提取失败: {str(e)}'})

@app.route('/api/add', methods=['POST'])
def add_keys():
    """添加密钥"""
    try:
        data = request.get_json()
        keys_text = data.get('keys', '').strip()
        
        if not keys_text:
            return jsonify({'success': False, 'message': '请输入要添加的密钥'})
        
        # 分割密钥，过滤空行和注释
        keys = []
        for line in keys_text.split('\n'):
            line = line.strip()
            if line and not line.startswith('#'):
                keys.append(line)
        
        if not keys:
            return jsonify({'success': False, 'message': '没有找到有效的密钥'})
        
        added_count = manager.add_keys(keys)
        
        return jsonify({
            'success': True,
            'message': f'成功添加 {added_count} 个密钥',
            'added_count': added_count,
            'total_attempted': len(keys)
        })
    
    except Exception as e:
        return jsonify({'success': False, 'message': f'添加失败: {str(e)}'})

@app.route('/api/available')
def get_available_keys():
    """获取可用密钥列表"""
    try:
        keys = manager.keys_data.get('keys', [])
        
        # 只返回部分信息，不暴露完整密钥
        key_list = []
        for i, key_info in enumerate(keys):
            key_list.append({
                'index': i + 1,
                'preview': key_info['key'][:12] + '...' if len(key_info['key']) > 12 else key_info['key'],
                'added_at': key_info['added_at'][:19]
            })
        
        return jsonify({
            'success': True,
            'keys': key_list,
            'count': len(key_list)
        })
    
    except Exception as e:
        return jsonify({'success': False, 'message': f'获取失败: {str(e)}'})

@app.route('/api/clear-used', methods=['POST'])
def clear_used_keys():
    """清空已使用密钥记录"""
    try:
        cleared_count = manager.clear_used_keys()
        return jsonify({
            'success': True,
            'message': f'已清空 {cleared_count} 个已使用密钥的记录',
            'cleared_count': cleared_count
        })
    
    except Exception as e:
        return jsonify({'success': False, 'message': f'清空失败: {str(e)}'})

if __name__ == '__main__':
    # 创建templates目录
    if not os.path.exists('templates'):
        os.makedirs('templates')
    
    print("API密钥管理系统 - Web版本")
    print("访问地址: http://localhost:5000")
    print("按 Ctrl+C 停止服务器")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
