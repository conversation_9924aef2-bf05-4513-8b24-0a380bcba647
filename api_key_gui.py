#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API密钥管理系统 - 图形界面版本
基于tkinter的桌面应用程序
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, filedialog
import json
import os
import pyperclip
import threading
import time
import re
from datetime import datetime
from api_key_manager import APIKeyManager

class APIKeyGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("API密钥提取管理系统 v2.0")
        self.root.geometry("1000x700")
        self.root.resizable(True, True)

        # 初始化管理器
        self.manager = APIKeyManager()

        # 初始化变量
        self.auto_refresh = tk.BooleanVar(value=True)
        self.show_full_keys = tk.BooleanVar(value=False)
        self.dark_mode = tk.BooleanVar(value=False)
        self.last_extracted_keys = []
        self.bound_folders = []  # 绑定的文件夹列表
        self.current_folder = None  # 当前选中的文件夹

        # 设置样式
        self.setup_styles()

        # 创建菜单
        self.create_menu()

        # 创建界面
        self.create_widgets()

        # 绑定快捷键
        self.bind_shortcuts()

        # 更新状态显示
        self.update_status()

        # 启动自动刷新
        self.start_auto_refresh()
    
    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')

        # 配置样式
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'))
        style.configure('Status.TLabel', font=('Arial', 10))
        style.configure('Action.TButton', font=('Arial', 10, 'bold'))
        style.configure('Success.TLabel', foreground='green')
        style.configure('Warning.TLabel', foreground='orange')
        style.configure('Error.TLabel', foreground='red')

    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="导入密钥文件...", command=self.import_from_file, accelerator="Ctrl+O")
        file_menu.add_command(label="导出可用密钥...", command=self.export_available_keys, accelerator="Ctrl+E")
        file_menu.add_command(label="导出已使用密钥...", command=self.export_used_keys)
        file_menu.add_separator()
        file_menu.add_command(label="绑定文件夹...", command=self.bind_folder, accelerator="Ctrl+D")
        file_menu.add_command(label="管理绑定文件夹...", command=self.manage_folders)
        file_menu.add_command(label="从文件夹提取...", command=self.extract_from_folder, accelerator="Ctrl+R")
        file_menu.add_separator()
        file_menu.add_command(label="备份数据...", command=self.backup_data, accelerator="Ctrl+B")
        file_menu.add_command(label="恢复数据...", command=self.restore_data)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit, accelerator="Ctrl+Q")

        # 编辑菜单
        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="编辑", menu=edit_menu)
        edit_menu.add_command(label="复制最后提取的密钥", command=self.copy_last_extracted, accelerator="Ctrl+C")
        edit_menu.add_command(label="清空输入框", command=self.clear_inputs, accelerator="Ctrl+L")
        edit_menu.add_separator()
        edit_menu.add_command(label="查找密钥...", command=self.search_keys, accelerator="Ctrl+F")

        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="密钥验证器", command=self.open_key_validator)
        tools_menu.add_command(label="批量操作", command=self.open_batch_operations)
        tools_menu.add_command(label="统计信息", command=self.show_statistics)
        tools_menu.add_separator()
        tools_menu.add_checkbutton(label="自动刷新", variable=self.auto_refresh)
        tools_menu.add_checkbutton(label="显示完整密钥", variable=self.show_full_keys)

        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="快捷键", command=self.show_shortcuts)
        help_menu.add_command(label="关于", command=self.show_about)

    def bind_shortcuts(self):
        """绑定快捷键"""
        self.root.bind('<Control-o>', lambda e: self.import_from_file())
        self.root.bind('<Control-e>', lambda e: self.export_available_keys())
        self.root.bind('<Control-b>', lambda e: self.backup_data())
        self.root.bind('<Control-c>', lambda e: self.copy_last_extracted())
        self.root.bind('<Control-l>', lambda e: self.clear_inputs())
        self.root.bind('<Control-f>', lambda e: self.search_keys())
        self.root.bind('<Control-d>', lambda e: self.bind_folder())
        self.root.bind('<Control-r>', lambda e: self.extract_from_folder())
        self.root.bind('<Control-q>', lambda e: self.root.quit())
        self.root.bind('<F5>', lambda e: self.update_status())
        self.root.bind('<Return>', self.on_enter_pressed)
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="API密钥提取管理系统", style='Title.TLabel')
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 状态信息框架
        status_frame = ttk.LabelFrame(main_frame, text="系统状态", padding="10")
        status_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        status_frame.columnconfigure(1, weight=1)
        
        # 状态标签
        ttk.Label(status_frame, text="可用密钥:").grid(row=0, column=0, sticky=tk.W)
        self.available_label = ttk.Label(status_frame, text="0", style='Status.TLabel')
        self.available_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        ttk.Label(status_frame, text="已使用密钥:").grid(row=1, column=0, sticky=tk.W)
        self.used_label = ttk.Label(status_frame, text="0", style='Status.TLabel')
        self.used_label.grid(row=1, column=1, sticky=tk.W, padx=(10, 0))
        
        ttk.Label(status_frame, text="最后更新:").grid(row=2, column=0, sticky=tk.W)
        self.update_label = ttk.Label(status_frame, text="未知", style='Status.TLabel')
        self.update_label.grid(row=2, column=1, sticky=tk.W, padx=(10, 0))
        
        # 主要操作区域
        operation_frame = ttk.Frame(main_frame)
        operation_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        operation_frame.columnconfigure(0, weight=1)
        operation_frame.columnconfigure(1, weight=1)
        operation_frame.rowconfigure(1, weight=1)
        
        # 左侧：提取密钥
        extract_frame = ttk.LabelFrame(operation_frame, text="提取密钥", padding="10")
        extract_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        
        ttk.Label(extract_frame, text="提取数量:").grid(row=0, column=0, sticky=tk.W)
        self.extract_entry = ttk.Entry(extract_frame, width=10)
        self.extract_entry.grid(row=0, column=1, padx=(10, 0))
        
        extract_btn = ttk.Button(extract_frame, text="提取密钥", style='Action.TButton',
                                command=self.extract_keys)
        extract_btn.grid(row=1, column=0, columnspan=2, pady=(10, 0), sticky=(tk.W, tk.E))
        
        # 右侧：添加密钥
        add_frame = ttk.LabelFrame(operation_frame, text="添加密钥", padding="10")
        add_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))
        
        ttk.Label(add_frame, text="密钥列表 (每行一个):").grid(row=0, column=0, sticky=tk.W)
        
        self.add_text = scrolledtext.ScrolledText(add_frame, height=6, width=30)
        self.add_text.grid(row=1, column=0, pady=(5, 0), sticky=(tk.W, tk.E, tk.N, tk.S))
        add_frame.rowconfigure(1, weight=1)
        add_frame.columnconfigure(0, weight=1)
        
        add_btn = ttk.Button(add_frame, text="添加密钥", style='Action.TButton',
                            command=self.add_keys)
        add_btn.grid(row=2, column=0, pady=(10, 0), sticky=(tk.W, tk.E))
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(operation_frame, text="操作结果", padding="10")
        result_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        result_frame.rowconfigure(0, weight=1)
        result_frame.columnconfigure(0, weight=1)
        
        self.result_text = scrolledtext.ScrolledText(result_frame, height=10)
        self.result_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 文件夹管理区
        folder_frame = ttk.LabelFrame(operation_frame, text="文件夹管理", padding="10")
        folder_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        folder_frame.columnconfigure(1, weight=1)

        # 当前文件夹显示
        ttk.Label(folder_frame, text="当前文件夹:").grid(row=0, column=0, sticky=tk.W)
        self.current_folder_label = ttk.Label(folder_frame, text="未选择", foreground="gray")
        self.current_folder_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))

        # 文件夹操作按钮
        folder_btn_frame = ttk.Frame(folder_frame)
        folder_btn_frame.grid(row=1, column=0, columnspan=2, pady=(10, 0), sticky=(tk.W, tk.E))
        folder_btn_frame.columnconfigure(0, weight=1)
        folder_btn_frame.columnconfigure(1, weight=1)
        folder_btn_frame.columnconfigure(2, weight=1)

        ttk.Button(folder_btn_frame, text="📁 绑定文件夹", command=self.bind_folder).grid(row=0, column=0, padx=2, sticky=(tk.W, tk.E))
        ttk.Button(folder_btn_frame, text="📋 管理文件夹", command=self.manage_folders).grid(row=0, column=1, padx=2, sticky=(tk.W, tk.E))
        ttk.Button(folder_btn_frame, text="📂 从文件夹提取", command=self.extract_from_folder).grid(row=0, column=2, padx=2, sticky=(tk.W, tk.E))

        # 高级功能区
        advanced_frame = ttk.LabelFrame(operation_frame, text="高级功能", padding="10")
        advanced_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        advanced_frame.columnconfigure(0, weight=1)
        advanced_frame.columnconfigure(1, weight=1)
        advanced_frame.columnconfigure(2, weight=1)

        # 快速操作按钮
        ttk.Button(advanced_frame, text="🔍 搜索密钥", command=self.search_keys).grid(row=0, column=0, padx=5, sticky=(tk.W, tk.E))
        ttk.Button(advanced_frame, text="📊 统计信息", command=self.show_statistics).grid(row=0, column=1, padx=5, sticky=(tk.W, tk.E))
        ttk.Button(advanced_frame, text="🔧 批量操作", command=self.open_batch_operations).grid(row=0, column=2, padx=5, sticky=(tk.W, tk.E))

        ttk.Button(advanced_frame, text="✅ 验证密钥", command=self.open_key_validator).grid(row=1, column=0, padx=5, pady=(5, 0), sticky=(tk.W, tk.E))
        ttk.Button(advanced_frame, text="📋 复制最后提取", command=self.copy_last_extracted).grid(row=1, column=1, padx=5, pady=(5, 0), sticky=(tk.W, tk.E))
        ttk.Button(advanced_frame, text="💾 备份数据", command=self.backup_data).grid(row=1, column=2, padx=5, pady=(5, 0), sticky=(tk.W, tk.E))

        # 底部按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=5, column=0, columnspan=2, pady=(10, 0))

        ttk.Button(button_frame, text="查看可用密钥", command=self.show_available_keys).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="清空已使用记录", command=self.clear_used_keys).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="刷新状态", command=self.update_status).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="清空结果", command=self.clear_result).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="导入文件", command=self.import_from_file).pack(side=tk.LEFT, padx=(5, 0))
    
    def update_status(self):
        """更新状态显示"""
        self.available_label.config(text=str(self.manager.get_available_count()))
        self.used_label.config(text=str(self.manager.get_used_count()))
        
        last_updated = self.manager.keys_data.get('last_updated', '未知')
        if last_updated != '未知':
            # 格式化时间显示
            try:
                dt = datetime.fromisoformat(last_updated.replace('Z', '+00:00'))
                formatted_time = dt.strftime('%Y-%m-%d %H:%M:%S')
                self.update_label.config(text=formatted_time)
            except:
                self.update_label.config(text=last_updated[:19])
        else:
            self.update_label.config(text=last_updated)
    
    def extract_keys(self):
        """提取密钥"""
        try:
            count_str = self.extract_entry.get().strip()
            if not count_str:
                messagebox.showwarning("输入错误", "请输入要提取的密钥数量")
                return
            
            count = int(count_str)
            if count <= 0:
                messagebox.showwarning("输入错误", "提取数量必须大于0")
                return
            
            # 检查可用密钥数量
            available = self.manager.get_available_count()
            if available == 0:
                messagebox.showinfo("提示", "没有可用的密钥")
                return
            
            # 提取密钥
            extracted = self.manager.extract_keys(count)

            if extracted:
                # 保存最后提取的密钥
                self.last_extracted_keys = extracted.copy()

                result = f"成功提取 {len(extracted)} 个密钥:\n\n"
                for i, key in enumerate(extracted, 1):
                    result += f"{i}. {key}\n"

                result += f"\n💡 提示: 使用 Ctrl+C 可快速复制这些密钥"

                self.result_text.delete(1.0, tk.END)
                self.result_text.insert(tk.END, result)

                # 清空输入框
                self.extract_entry.delete(0, tk.END)

                # 更新状态
                self.update_status()

                # 自动复制到剪贴板
                try:
                    keys_text = '\n'.join(extracted)
                    pyperclip.copy(keys_text)
                    messagebox.showinfo("成功", f"已提取 {len(extracted)} 个密钥\n密钥已自动复制到剪贴板")
                except:
                    messagebox.showinfo("成功", f"已提取 {len(extracted)} 个密钥")
            else:
                messagebox.showinfo("提示", "没有提取到密钥")
        
        except ValueError:
            messagebox.showerror("输入错误", "请输入有效的数字")
        except Exception as e:
            messagebox.showerror("错误", f"提取密钥时发生错误: {str(e)}")
    
    def add_keys(self):
        """添加密钥"""
        try:
            keys_text = self.add_text.get(1.0, tk.END).strip()
            if not keys_text:
                messagebox.showwarning("输入错误", "请输入要添加的密钥")
                return
            
            # 分割密钥
            keys = [key.strip() for key in keys_text.split('\n') if key.strip()]
            
            if not keys:
                messagebox.showwarning("输入错误", "没有找到有效的密钥")
                return
            
            # 添加密钥
            added_count = self.manager.add_keys(keys)
            
            result = f"尝试添加 {len(keys)} 个密钥\n"
            result += f"成功添加 {added_count} 个新密钥\n"
            if added_count < len(keys):
                result += f"跳过 {len(keys) - added_count} 个重复密钥\n"
            
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, result)
            
            # 清空输入框
            self.add_text.delete(1.0, tk.END)
            
            # 更新状态
            self.update_status()
            
            messagebox.showinfo("成功", f"成功添加 {added_count} 个密钥")
        
        except Exception as e:
            messagebox.showerror("错误", f"添加密钥时发生错误: {str(e)}")
    
    def show_available_keys(self):
        """显示可用密钥"""
        keys = self.manager.keys_data.get('keys', [])
        
        if not keys:
            result = "没有可用的密钥"
        else:
            result = f"可用密钥列表 ({len(keys)}个):\n\n"
            for i, key_info in enumerate(keys, 1):
                key_display = key_info['key'][:12] + "..." if len(key_info['key']) > 12 else key_info['key']
                added_time = key_info['added_at'][:19]
                result += f"{i}. {key_display} (添加时间: {added_time})\n"
        
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, result)
    
    def clear_used_keys(self):
        """清空已使用密钥记录"""
        if messagebox.askyesno("确认", "确定要清空所有已使用密钥的记录吗？"):
            try:
                cleared_count = self.manager.clear_used_keys()
                self.update_status()
                
                result = f"已清空 {cleared_count} 个已使用密钥的记录"
                self.result_text.delete(1.0, tk.END)
                self.result_text.insert(tk.END, result)
                
                messagebox.showinfo("成功", f"已清空 {cleared_count} 个记录")
            except Exception as e:
                messagebox.showerror("错误", f"清空记录时发生错误: {str(e)}")
    
    def clear_result(self):
        """清空结果显示"""
        self.result_text.delete(1.0, tk.END)

    def import_from_file(self):
        """从文件导入密钥"""
        from tkinter import filedialog

        file_path = filedialog.askopenfilename(
            title="选择密钥文件",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 过滤掉注释行和空行
                keys = []
                for line in content.split('\n'):
                    line = line.strip()
                    if line and not line.startswith('#'):
                        keys.append(line)

                if keys:
                    added_count = self.manager.add_keys(keys)

                    result = f"从文件导入:\n"
                    result += f"文件: {os.path.basename(file_path)}\n"
                    result += f"尝试导入 {len(keys)} 个密钥\n"
                    result += f"成功添加 {added_count} 个新密钥\n"

                    self.result_text.delete(1.0, tk.END)
                    self.result_text.insert(tk.END, result)

                    self.update_status()
                    messagebox.showinfo("成功", f"成功导入 {added_count} 个密钥")
                else:
                    messagebox.showwarning("警告", "文件中没有找到有效的密钥")

            except Exception as e:
                messagebox.showerror("错误", f"导入文件时发生错误: {str(e)}")

    def start_auto_refresh(self):
        """启动自动刷新"""
        def refresh_loop():
            while True:
                if self.auto_refresh.get():
                    try:
                        self.root.after(0, self.update_status)
                    except:
                        break
                time.sleep(5)  # 每5秒刷新一次

        thread = threading.Thread(target=refresh_loop, daemon=True)
        thread.start()

    def copy_last_extracted(self):
        """复制最后提取的密钥"""
        if self.last_extracted_keys:
            try:
                keys_text = '\n'.join(self.last_extracted_keys)
                pyperclip.copy(keys_text)
                messagebox.showinfo("成功", f"已复制 {len(self.last_extracted_keys)} 个密钥到剪贴板")
            except Exception as e:
                messagebox.showerror("错误", f"复制失败: {str(e)}")
        else:
            messagebox.showwarning("提示", "没有可复制的密钥")

    def clear_inputs(self):
        """清空所有输入框"""
        self.extract_entry.delete(0, tk.END)
        self.add_text.delete(1.0, tk.END)
        self.result_text.delete(1.0, tk.END)

    def on_enter_pressed(self, event):
        """处理回车键按下事件"""
        widget = event.widget
        if widget == self.extract_entry:
            self.extract_keys()
        elif widget == self.add_text:
            self.add_keys()

    def export_available_keys(self):
        """导出可用密钥"""
        keys = self.manager.keys_data.get('keys', [])
        if not keys:
            messagebox.showwarning("提示", "没有可用的密钥可导出")
            return

        file_path = filedialog.asksaveasfilename(
            title="导出可用密钥",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"# 可用密钥导出 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"# 总计: {len(keys)} 个密钥\n\n")
                    for key_info in keys:
                        f.write(f"{key_info['key']}\n")

                messagebox.showinfo("成功", f"已导出 {len(keys)} 个密钥到:\n{file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"导出失败: {str(e)}")

    def export_used_keys(self):
        """导出已使用密钥"""
        used_keys = self.manager.keys_data.get('used_keys', [])
        if not used_keys:
            messagebox.showwarning("提示", "没有已使用的密钥可导出")
            return

        file_path = filedialog.asksaveasfilename(
            title="导出已使用密钥",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"# 已使用密钥导出 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"# 总计: {len(used_keys)} 个密钥\n\n")
                    for key_info in used_keys:
                        used_time = key_info.get('used_at', '未知')
                        f.write(f"{key_info['key']} # 使用时间: {used_time[:19]}\n")

                messagebox.showinfo("成功", f"已导出 {len(used_keys)} 个已使用密钥到:\n{file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"导出失败: {str(e)}")

    def backup_data(self):
        """备份数据"""
        file_path = filedialog.asksaveasfilename(
            title="备份数据",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                import shutil
                shutil.copy2(self.manager.config_file, file_path)
                messagebox.showinfo("成功", f"数据已备份到:\n{file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"备份失败: {str(e)}")

    def restore_data(self):
        """恢复数据"""
        if messagebox.askyesno("确认", "恢复数据将覆盖当前所有数据，确定继续吗？"):
            file_path = filedialog.askopenfilename(
                title="选择备份文件",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
            )

            if file_path:
                try:
                    import shutil
                    shutil.copy2(file_path, self.manager.config_file)
                    self.manager = APIKeyManager()  # 重新加载数据
                    self.update_status()
                    messagebox.showinfo("成功", "数据恢复成功")
                except Exception as e:
                    messagebox.showerror("错误", f"恢复失败: {str(e)}")

    def search_keys(self):
        """搜索密钥"""
        search_window = tk.Toplevel(self.root)
        search_window.title("搜索密钥")
        search_window.geometry("500x400")
        search_window.resizable(False, False)

        # 搜索框
        search_frame = ttk.Frame(search_window, padding="10")
        search_frame.pack(fill=tk.X)

        ttk.Label(search_frame, text="搜索关键词:").pack(side=tk.LEFT)
        search_entry = ttk.Entry(search_frame, width=30)
        search_entry.pack(side=tk.LEFT, padx=(10, 0))

        def do_search():
            keyword = search_entry.get().strip()
            if not keyword:
                return

            results = []
            # 搜索可用密钥
            for key_info in self.manager.keys_data.get('keys', []):
                if keyword.lower() in key_info['key'].lower():
                    results.append(('可用', key_info))

            # 搜索已使用密钥
            for key_info in self.manager.keys_data.get('used_keys', []):
                if keyword.lower() in key_info['key'].lower():
                    results.append(('已使用', key_info))

            # 显示结果
            result_text.delete(1.0, tk.END)
            if results:
                result_text.insert(tk.END, f"找到 {len(results)} 个匹配的密钥:\n\n")
                for i, (status, key_info) in enumerate(results, 1):
                    key_preview = key_info['key'][:20] + "..." if len(key_info['key']) > 20 else key_info['key']
                    result_text.insert(tk.END, f"{i}. [{status}] {key_preview}\n")
            else:
                result_text.insert(tk.END, "没有找到匹配的密钥")

        search_btn = ttk.Button(search_frame, text="搜索", command=do_search)
        search_btn.pack(side=tk.LEFT, padx=(10, 0))

        # 结果显示
        result_frame = ttk.Frame(search_window, padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True)

        ttk.Label(result_frame, text="搜索结果:").pack(anchor=tk.W)
        result_text = scrolledtext.ScrolledText(result_frame, height=15)
        result_text.pack(fill=tk.BOTH, expand=True, pady=(5, 0))

        # 绑定回车键
        search_entry.bind('<Return>', lambda e: do_search())
        search_entry.focus()

    def show_statistics(self):
        """显示统计信息"""
        stats_window = tk.Toplevel(self.root)
        stats_window.title("统计信息")
        stats_window.geometry("400x300")
        stats_window.resizable(False, False)

        stats_frame = ttk.Frame(stats_window, padding="20")
        stats_frame.pack(fill=tk.BOTH, expand=True)

        # 计算统计数据
        available_count = self.manager.get_available_count()
        used_count = self.manager.get_used_count()
        total_count = available_count + used_count

        # 计算使用率
        usage_rate = (used_count / total_count * 100) if total_count > 0 else 0

        # 显示统计信息
        stats_text = f"""
📊 密钥统计信息

总密钥数量: {total_count}
可用密钥: {available_count}
已使用密钥: {used_count}
使用率: {usage_rate:.1f}%

📅 时间信息
创建时间: {self.manager.keys_data.get('created_at', '未知')[:19]}
最后更新: {self.manager.keys_data.get('last_updated', '未知')[:19]}

💾 文件信息
数据文件: {self.manager.config_file}
文件大小: {self.get_file_size()} KB
        """

        stats_label = ttk.Label(stats_frame, text=stats_text, font=('Arial', 10))
        stats_label.pack()

    def get_file_size(self):
        """获取数据文件大小"""
        try:
            size = os.path.getsize(self.manager.config_file)
            return round(size / 1024, 2)
        except:
            return 0

    def open_key_validator(self):
        """打开密钥验证器"""
        validator_window = tk.Toplevel(self.root)
        validator_window.title("密钥验证器")
        validator_window.geometry("500x400")

        main_frame = ttk.Frame(validator_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 输入区域
        ttk.Label(main_frame, text="输入要验证的密钥:").pack(anchor=tk.W)
        key_entry = ttk.Entry(main_frame, width=60)
        key_entry.pack(fill=tk.X, pady=(5, 10))

        def validate_key():
            key = key_entry.get().strip()
            if not key:
                return

            # 基本验证规则
            result = "验证结果:\n\n"

            # 长度检查
            if len(key) < 20:
                result += "❌ 密钥长度过短 (< 20字符)\n"
            elif len(key) > 100:
                result += "⚠️ 密钥长度较长 (> 100字符)\n"
            else:
                result += "✅ 密钥长度合适\n"

            # 格式检查
            if key.startswith('sk-'):
                result += "✅ OpenAI API密钥格式\n"
            elif key.startswith('api-key-'):
                result += "✅ 标准API密钥格式\n"
            elif re.match(r'^[a-zA-Z0-9_-]+$', key):
                result += "✅ 字符格式有效\n"
            else:
                result += "⚠️ 包含特殊字符\n"

            # 检查是否已存在
            existing = False
            for key_info in self.manager.keys_data.get('keys', []):
                if key_info['key'] == key:
                    result += "⚠️ 密钥已存在于可用列表中\n"
                    existing = True
                    break

            for key_info in self.manager.keys_data.get('used_keys', []):
                if key_info['key'] == key:
                    result += "❌ 密钥已被使用\n"
                    existing = True
                    break

            if not existing:
                result += "✅ 密钥未重复\n"

            result_text.delete(1.0, tk.END)
            result_text.insert(tk.END, result)

        validate_btn = ttk.Button(main_frame, text="验证密钥", command=validate_key)
        validate_btn.pack(pady=(0, 10))

        # 结果显示
        ttk.Label(main_frame, text="验证结果:").pack(anchor=tk.W)
        result_text = scrolledtext.ScrolledText(main_frame, height=15)
        result_text.pack(fill=tk.BOTH, expand=True)

        key_entry.bind('<Return>', lambda e: validate_key())
        key_entry.focus()

    def open_batch_operations(self):
        """打开批量操作窗口"""
        batch_window = tk.Toplevel(self.root)
        batch_window.title("批量操作")
        batch_window.geometry("600x500")

        notebook = ttk.Notebook(batch_window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 批量添加标签页
        add_frame = ttk.Frame(notebook)
        notebook.add(add_frame, text="批量添加")

        ttk.Label(add_frame, text="从多个文件批量导入密钥:", font=('Arial', 12, 'bold')).pack(pady=10)

        files_listbox = tk.Listbox(add_frame, height=8)
        files_listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        def select_files():
            files = filedialog.askopenfilenames(
                title="选择密钥文件",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )
            files_listbox.delete(0, tk.END)
            for file in files:
                files_listbox.insert(tk.END, file)

        def batch_import():
            files = files_listbox.get(0, tk.END)
            if not files:
                messagebox.showwarning("提示", "请先选择文件")
                return

            total_added = 0
            for file_path in files:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    keys = []
                    for line in content.split('\n'):
                        line = line.strip()
                        if line and not line.startswith('#'):
                            keys.append(line)

                    added = self.manager.add_keys(keys)
                    total_added += added
                except Exception as e:
                    messagebox.showerror("错误", f"处理文件 {file_path} 时出错: {str(e)}")

            self.update_status()
            messagebox.showinfo("完成", f"批量导入完成，共添加 {total_added} 个密钥")

        btn_frame = ttk.Frame(add_frame)
        btn_frame.pack(pady=10)
        ttk.Button(btn_frame, text="选择文件", command=select_files).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="开始导入", command=batch_import).pack(side=tk.LEFT, padx=5)

        # 批量提取标签页
        extract_frame = ttk.Frame(notebook)
        notebook.add(extract_frame, text="批量提取")

        ttk.Label(extract_frame, text="批量提取密钥:", font=('Arial', 12, 'bold')).pack(pady=10)

        extract_info_frame = ttk.Frame(extract_frame)
        extract_info_frame.pack(pady=10)

        ttk.Label(extract_info_frame, text="每次提取数量:").grid(row=0, column=0, padx=5)
        batch_count_entry = ttk.Entry(extract_info_frame, width=10)
        batch_count_entry.grid(row=0, column=1, padx=5)

        ttk.Label(extract_info_frame, text="提取次数:").grid(row=0, column=2, padx=5)
        batch_times_entry = ttk.Entry(extract_info_frame, width=10)
        batch_times_entry.grid(row=0, column=3, padx=5)

        batch_result_text = scrolledtext.ScrolledText(extract_frame, height=15)
        batch_result_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        def batch_extract():
            try:
                count = int(batch_count_entry.get())
                times = int(batch_times_entry.get())

                if count <= 0 or times <= 0:
                    messagebox.showwarning("输入错误", "数量和次数必须大于0")
                    return

                batch_result_text.delete(1.0, tk.END)
                all_extracted = []

                for i in range(times):
                    extracted = self.manager.extract_keys(count)
                    if extracted:
                        batch_result_text.insert(tk.END, f"第 {i+1} 次提取 ({len(extracted)} 个):\n")
                        for key in extracted:
                            batch_result_text.insert(tk.END, f"  {key}\n")
                        batch_result_text.insert(tk.END, "\n")
                        all_extracted.extend(extracted)
                    else:
                        batch_result_text.insert(tk.END, f"第 {i+1} 次提取: 没有可用密钥\n")
                        break

                if all_extracted:
                    try:
                        pyperclip.copy('\n'.join(all_extracted))
                        batch_result_text.insert(tk.END, f"\n✅ 总计提取 {len(all_extracted)} 个密钥，已复制到剪贴板")
                    except:
                        batch_result_text.insert(tk.END, f"\n✅ 总计提取 {len(all_extracted)} 个密钥")

                self.update_status()

            except ValueError:
                messagebox.showerror("输入错误", "请输入有效的数字")

        ttk.Button(extract_frame, text="开始批量提取", command=batch_extract).pack(pady=10)

    def show_help(self):
        """显示帮助信息"""
        help_window = tk.Toplevel(self.root)
        help_window.title("使用说明")
        help_window.geometry("600x500")

        help_text = """
🔑 API密钥管理系统 v2.0 使用说明

📋 主要功能:
• 密钥提取: 输入数量，一键提取指定数量的密钥
• 批量添加: 支持手动输入和文件导入
• 搜索功能: 快速查找特定密钥
• 数据备份: 导出和备份密钥数据
• 统计分析: 查看使用情况和统计信息

⌨️ 快捷键:
• Ctrl+O: 导入文件
• Ctrl+E: 导出可用密钥
• Ctrl+B: 备份数据
• Ctrl+C: 复制最后提取的密钥
• Ctrl+L: 清空输入框
• Ctrl+F: 搜索密钥
• F5: 刷新状态
• Enter: 确认当前操作

🔧 高级功能:
• 密钥验证器: 检查密钥格式和有效性
• 批量操作: 批量导入和提取密钥
• 自动刷新: 实时更新状态信息
• 自动复制: 提取后自动复制到剪贴板

⚠️ 注意事项:
• 提取的密钥会被标记为已使用，不能再次提取
• 所有操作会自动保存到本地文件
• 建议定期备份重要数据
• 密钥信息仅在本地存储，确保数据安全

💡 使用技巧:
• 使用搜索功能快速定位特定密钥
• 利用批量操作提高工作效率
• 定期查看统计信息了解使用情况
• 使用验证器确保密钥格式正确
        """

        text_widget = scrolledtext.ScrolledText(help_window, wrap=tk.WORD)
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        text_widget.insert(tk.END, help_text)
        text_widget.config(state=tk.DISABLED)

    def show_shortcuts(self):
        """显示快捷键"""
        shortcuts_window = tk.Toplevel(self.root)
        shortcuts_window.title("快捷键列表")
        shortcuts_window.geometry("400x300")

        shortcuts_text = """
⌨️ 快捷键列表

文件操作:
Ctrl+O    导入密钥文件
Ctrl+E    导出可用密钥
Ctrl+B    备份数据
Ctrl+Q    退出程序

编辑操作:
Ctrl+C    复制最后提取的密钥
Ctrl+L    清空所有输入框
Ctrl+F    搜索密钥

其他操作:
F5        刷新状态
Enter     确认当前操作
        """

        text_widget = scrolledtext.ScrolledText(shortcuts_window, wrap=tk.WORD)
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        text_widget.insert(tk.END, shortcuts_text)
        text_widget.config(state=tk.DISABLED)

    def show_about(self):
        """显示关于信息"""
        messagebox.showinfo("关于",
            "API密钥管理系统 v2.0\n\n"
            "功能特点:\n"
            "• 图形化密钥管理\n"
            "• 一次性提取机制\n"
            "• 批量操作支持\n"
            "• 数据备份恢复\n"
            "• 密钥验证功能\n\n"
            "开发语言: Python + tkinter\n"
            "版本: 2.0\n"
            "更新时间: 2025-08-03")

    def bind_folder(self):
        """绑定文件夹"""
        folder_path = filedialog.askdirectory(title="选择要绑定的文件夹")

        if folder_path:
            if folder_path not in self.bound_folders:
                self.bound_folders.append(folder_path)
                self.current_folder = folder_path
                self.update_folder_display()
                self.save_folder_config()
                messagebox.showinfo("成功", f"已绑定文件夹:\n{folder_path}")
            else:
                messagebox.showwarning("提示", "该文件夹已经绑定")

    def manage_folders(self):
        """管理绑定的文件夹"""
        manage_window = tk.Toplevel(self.root)
        manage_window.title("管理绑定文件夹")
        manage_window.geometry("600x400")
        manage_window.resizable(True, True)

        main_frame = ttk.Frame(manage_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 文件夹列表
        ttk.Label(main_frame, text="已绑定的文件夹:", font=('Arial', 12, 'bold')).pack(anchor=tk.W, pady=(0, 10))

        # 列表框和滚动条
        list_frame = ttk.Frame(main_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        folders_listbox = tk.Listbox(list_frame, selectmode=tk.SINGLE)
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=folders_listbox.yview)
        folders_listbox.configure(yscrollcommand=scrollbar.set)

        folders_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 刷新列表
        def refresh_list():
            folders_listbox.delete(0, tk.END)
            for folder in self.bound_folders:
                display_text = f"{'[当前] ' if folder == self.current_folder else ''}{folder}"
                folders_listbox.insert(tk.END, display_text)

        refresh_list()

        # 按钮区域
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(fill=tk.X, pady=(10, 0))

        def add_folder():
            folder_path = filedialog.askdirectory(title="选择要添加的文件夹")
            if folder_path and folder_path not in self.bound_folders:
                self.bound_folders.append(folder_path)
                self.save_folder_config()
                refresh_list()
                messagebox.showinfo("成功", "文件夹已添加")

        def remove_folder():
            selection = folders_listbox.curselection()
            if selection:
                index = selection[0]
                folder_to_remove = self.bound_folders[index]
                if messagebox.askyesno("确认", f"确定要移除文件夹吗?\n{folder_to_remove}"):
                    self.bound_folders.pop(index)
                    if folder_to_remove == self.current_folder:
                        self.current_folder = self.bound_folders[0] if self.bound_folders else None
                        self.update_folder_display()
                    self.save_folder_config()
                    refresh_list()
            else:
                messagebox.showwarning("提示", "请先选择要移除的文件夹")

        def set_current():
            selection = folders_listbox.curselection()
            if selection:
                index = selection[0]
                self.current_folder = self.bound_folders[index]
                self.update_folder_display()
                self.save_folder_config()
                refresh_list()
                messagebox.showinfo("成功", "已设置为当前文件夹")
            else:
                messagebox.showwarning("提示", "请先选择文件夹")

        ttk.Button(btn_frame, text="添加文件夹", command=add_folder).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_frame, text="移除文件夹", command=remove_folder).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="设为当前", command=set_current).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="刷新", command=refresh_list).pack(side=tk.LEFT, padx=5)

    def extract_from_folder(self):
        """从文件夹提取密钥"""
        if not self.current_folder:
            messagebox.showwarning("提示", "请先绑定并选择一个文件夹")
            return

        if not os.path.exists(self.current_folder):
            messagebox.showerror("错误", f"文件夹不存在:\n{self.current_folder}")
            return

        extract_window = tk.Toplevel(self.root)
        extract_window.title("从文件夹提取密钥")
        extract_window.geometry("700x500")
        extract_window.resizable(True, True)

        main_frame = ttk.Frame(extract_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 当前文件夹显示
        folder_frame = ttk.LabelFrame(main_frame, text="当前文件夹", padding="10")
        folder_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(folder_frame, text=self.current_folder, foreground="blue").pack(anchor=tk.W)

        # 文件列表
        files_frame = ttk.LabelFrame(main_frame, text="文件列表", padding="10")
        files_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 文件列表框
        list_frame = ttk.Frame(files_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)

        files_listbox = tk.Listbox(list_frame, selectmode=tk.MULTIPLE)
        files_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=files_listbox.yview)
        files_listbox.configure(yscrollcommand=files_scrollbar.set)

        files_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        files_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 扫描文件
        def scan_files():
            files_listbox.delete(0, tk.END)
            try:
                for filename in os.listdir(self.current_folder):
                    file_path = os.path.join(self.current_folder, filename)
                    if os.path.isfile(file_path) and filename.lower().endswith(('.txt', '.key', '.api', '.keys')):
                        # 显示文件名和大小
                        size = os.path.getsize(file_path)
                        size_str = f"{size} bytes" if size < 1024 else f"{size//1024} KB"
                        display_text = f"{filename} ({size_str})"
                        files_listbox.insert(tk.END, display_text)
            except Exception as e:
                messagebox.showerror("错误", f"扫描文件夹失败: {str(e)}")

        scan_files()

        # 操作区域
        operation_frame = ttk.Frame(main_frame)
        operation_frame.pack(fill=tk.X, pady=(10, 0))

        # 文件操作按钮
        file_btn_frame = ttk.Frame(operation_frame)
        file_btn_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(file_btn_frame, text="🔄 刷新文件列表", command=scan_files).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(file_btn_frame, text="📁 打开文件夹", command=lambda: os.startfile(self.current_folder)).pack(side=tk.LEFT, padx=5)

        # 提取操作
        extract_frame = ttk.LabelFrame(operation_frame, text="提取操作", padding="10")
        extract_frame.pack(fill=tk.X)

        def extract_selected_files():
            selection = files_listbox.curselection()
            if not selection:
                messagebox.showwarning("提示", "请先选择要提取的文件")
                return

            total_keys = []
            processed_files = []

            for index in selection:
                filename = files_listbox.get(index).split(' (')[0]  # 去掉大小信息
                file_path = os.path.join(self.current_folder, filename)

                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # 提取密钥
                    keys = []
                    for line in content.split('\n'):
                        line = line.strip()
                        if line and not line.startswith('#'):
                            keys.append(line)

                    if keys:
                        total_keys.extend(keys)
                        processed_files.append(f"{filename} ({len(keys)}个密钥)")

                except Exception as e:
                    messagebox.showerror("错误", f"读取文件 {filename} 失败: {str(e)}")
                    continue

            if total_keys:
                # 添加到管理器
                added_count = self.manager.add_keys(total_keys)

                # 显示结果
                result = f"文件夹提取完成!\n\n"
                result += f"处理文件:\n"
                for file_info in processed_files:
                    result += f"• {file_info}\n"
                result += f"\n总计提取: {len(total_keys)} 个密钥\n"
                result += f"成功添加: {added_count} 个新密钥\n"
                result += f"跳过重复: {len(total_keys) - added_count} 个密钥"

                self.result_text.delete(1.0, tk.END)
                self.result_text.insert(tk.END, result)

                # 更新状态
                self.update_status()

                messagebox.showinfo("完成", f"成功添加 {added_count} 个新密钥")
                extract_window.destroy()
            else:
                messagebox.showwarning("提示", "所选文件中没有找到有效的密钥")

        ttk.Button(extract_frame, text="📥 提取选中文件的密钥", command=extract_selected_files).pack(pady=5)

    def update_folder_display(self):
        """更新文件夹显示"""
        if self.current_folder:
            # 只显示文件夹名，不显示完整路径
            folder_name = os.path.basename(self.current_folder)
            if not folder_name:  # 根目录情况
                folder_name = self.current_folder
            self.current_folder_label.config(text=folder_name, foreground="blue")
        else:
            self.current_folder_label.config(text="未选择", foreground="gray")

    def save_folder_config(self):
        """保存文件夹配置"""
        config = {
            'bound_folders': self.bound_folders,
            'current_folder': self.current_folder
        }
        try:
            with open('folder_config.json', 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存文件夹配置失败: {e}")

    def load_folder_config(self):
        """加载文件夹配置"""
        try:
            if os.path.exists('folder_config.json'):
                with open('folder_config.json', 'r', encoding='utf-8') as f:
                    config = json.load(f)
                self.bound_folders = config.get('bound_folders', [])
                self.current_folder = config.get('current_folder', None)

                # 检查文件夹是否仍然存在
                valid_folders = []
                for folder in self.bound_folders:
                    if os.path.exists(folder):
                        valid_folders.append(folder)

                self.bound_folders = valid_folders

                # 检查当前文件夹是否有效
                if self.current_folder and not os.path.exists(self.current_folder):
                    self.current_folder = self.bound_folders[0] if self.bound_folders else None

                self.update_folder_display()
        except Exception as e:
            print(f"加载文件夹配置失败: {e}")
            self.bound_folders = []
            self.current_folder = None


def main():
    """主函数"""
    root = tk.Tk()
    app = APIKeyGUI(root)
    
    # 设置窗口图标（如果有的话）
    try:
        # root.iconbitmap('icon.ico')  # 如果有图标文件
        pass
    except:
        pass
    
    # 启动主循环
    root.mainloop()


if __name__ == "__main__":
    main()
