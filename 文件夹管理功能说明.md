# 文件夹管理功能说明

## 🎯 功能概述

新增的文件夹管理功能允许您绑定多个文件夹，并从这些文件夹中的特定文件提取API密钥。这个功能特别适合管理分散在不同文件夹中的密钥文件。

## 📁 主要功能

### 1. 文件夹绑定
- **绑定文件夹**: 将常用的密钥文件夹添加到系统中
- **多文件夹支持**: 可以绑定多个不同的文件夹
- **当前文件夹**: 设置一个主要工作文件夹
- **自动保存**: 绑定信息自动保存，重启后恢复

### 2. 文件夹管理
- **添加文件夹**: 随时添加新的文件夹
- **移除文件夹**: 删除不需要的文件夹绑定
- **切换文件夹**: 快速切换当前工作文件夹
- **文件夹验证**: 自动检查文件夹是否存在

### 3. 文件提取
- **文件扫描**: 自动扫描文件夹中的密钥文件
- **多选提取**: 可以选择多个文件同时提取
- **格式支持**: 支持 .txt, .key, .api, .keys 等格式
- **智能过滤**: 自动过滤注释和空行

## 🖥️ 界面布局

### 文件夹管理区
```
┌─────────────────────────────────────────────────────────────┐
│  文件夹管理                                                  │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ 当前文件夹: MyKeys                                      │ │
│  │                                                         │ │
│  │ [📁 绑定文件夹] [📋 管理文件夹] [📂 从文件夹提取]        │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## ⌨️ 快捷键

- **Ctrl+D**: 绑定新文件夹
- **Ctrl+R**: 从当前文件夹提取密钥

## 📋 使用流程

### 初次设置
1. **绑定文件夹**
   - 点击"📁 绑定文件夹"按钮
   - 选择包含密钥文件的文件夹
   - 系统自动设置为当前文件夹

2. **管理多个文件夹**
   - 点击"📋 管理文件夹"打开管理窗口
   - 添加更多文件夹
   - 设置主要工作文件夹

### 日常使用
1. **从文件夹提取密钥**
   - 点击"📂 从文件夹提取"
   - 查看文件夹中的密钥文件列表
   - 选择要提取的文件
   - 点击"📥 提取选中文件的密钥"

## 🔧 详细功能

### 绑定文件夹功能
```python
功能: 将文件夹添加到管理系统
操作: 文件 → 绑定文件夹 (Ctrl+D)
结果: 文件夹添加到绑定列表，设为当前文件夹
```

### 管理文件夹功能
```python
功能: 管理所有绑定的文件夹
操作: 文件 → 管理绑定文件夹
包含:
  - 查看所有绑定文件夹
  - 添加新文件夹
  - 移除文件夹
  - 设置当前文件夹
  - 刷新列表
```

### 从文件夹提取功能
```python
功能: 从当前文件夹的文件中提取密钥
操作: 文件 → 从文件夹提取 (Ctrl+R)
流程:
  1. 显示当前文件夹路径
  2. 扫描并列出密钥文件
  3. 选择要处理的文件
  4. 提取并添加密钥到系统
```

## 📄 支持的文件格式

### 密钥文件格式
- **.txt**: 文本文件
- **.key**: 密钥文件
- **.api**: API密钥文件
- **.keys**: 密钥集合文件

### 文件内容格式
```
# 这是注释，会被忽略
sk-1234567890abcdefghijklmnopqrstuvwxyz
api-key-example-111111111111111111111111111

# 另一个注释
sk-9876543210zyxwvutsrqponmlkjihgfedcba
```

## 🔄 自动化特性

### 配置保存
- **自动保存**: 所有文件夹绑定信息自动保存
- **配置文件**: 保存在 `folder_config.json`
- **启动恢复**: 程序启动时自动加载配置

### 文件夹验证
- **存在检查**: 启动时检查文件夹是否仍存在
- **自动清理**: 移除不存在的文件夹绑定
- **智能恢复**: 自动选择有效的当前文件夹

## 💡 使用技巧

### 组织建议
```
推荐的文件夹结构:
📁 API-Keys/
  ├── 📁 OpenAI/
  │   ├── production.txt
  │   └── development.txt
  ├── 📁 Google/
  │   ├── cloud-api.key
  │   └── maps-api.key
  └── 📁 Others/
      ├── github.txt
      └── stripe.api
```

### 工作流程
1. **按项目分类**: 为不同项目创建不同文件夹
2. **按环境分类**: 区分生产环境和开发环境密钥
3. **定期整理**: 定期清理无效的文件夹绑定
4. **备份重要**: 对重要的密钥文件夹进行备份

### 批量操作
- 使用多选功能一次处理多个文件
- 结合批量操作功能提高效率
- 利用搜索功能快速定位特定密钥

## ⚠️ 注意事项

### 安全考虑
- **文件权限**: 确保密钥文件夹有适当的访问权限
- **路径安全**: 避免绑定包含敏感信息的系统文件夹
- **备份保护**: 定期备份重要的密钥文件

### 性能优化
- **文件数量**: 避免在包含大量文件的文件夹中操作
- **文件大小**: 大文件可能影响扫描速度
- **网络路径**: 避免绑定网络路径，可能影响响应速度

### 兼容性
- **路径格式**: 支持Windows、Linux、macOS路径格式
- **文件编码**: 支持UTF-8编码的文本文件
- **特殊字符**: 避免文件名包含特殊字符

## 🔧 配置文件格式

### folder_config.json
```json
{
  "bound_folders": [
    "C:\\Users\\<USER>\\Documents\\API-Keys",
    "D:\\Projects\\Keys",
    "/home/<USER>/api-keys"
  ],
  "current_folder": "C:\\Users\\<USER>\\Documents\\API-Keys"
}
```

## 🚀 快速开始

### 第一步: 绑定文件夹
```
1. 点击 "📁 绑定文件夹" 或按 Ctrl+D
2. 选择包含密钥文件的文件夹
3. 确认绑定
```

### 第二步: 提取密钥
```
1. 点击 "📂 从文件夹提取" 或按 Ctrl+R
2. 查看文件列表
3. 选择要提取的文件
4. 点击 "📥 提取选中文件的密钥"
```

### 第三步: 管理文件夹
```
1. 点击 "📋 管理文件夹"
2. 添加更多文件夹
3. 设置当前工作文件夹
4. 移除不需要的文件夹
```

这个功能让您能够更高效地管理分散在不同位置的密钥文件，提供了完整的文件夹绑定和密钥提取解决方案！
