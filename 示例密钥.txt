# 示例API密钥文件
# 这些是示例密钥，仅用于测试，请替换为真实的API密钥

sk-test1234567890abcdefghijklmnopqrstuvwxyz
sk-demo9876543210zyxwvutsrqponmlkjihgfedcba
api-key-example-111111111111111111111111111
api-key-example-222222222222222222222222222
api-key-example-333333333333333333333333333
test-api-key-aaaaaaaaaaaaaaaaaaaaaaaaaaa
test-api-key-bbbbbbbbbbbbbbbbbbbbbbbbbbb
test-api-key-ccccccccccccccccccccccccccc
demo-key-12345678901234567890123456789
demo-key-98765432109876543210987654321

# 使用方法：
# 1. 将真实的API密钥替换上面的示例密钥
# 2. 运行 python api_key_manager.py
# 3. 选择"添加密钥"选项
# 4. 复制粘贴真实密钥到程序中
