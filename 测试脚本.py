#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API密钥管理系统测试脚本
用于演示系统功能
"""

from api_key_manager import APIKeyManager
import os

def test_api_key_manager():
    """测试API密钥管理器的功能"""
    print("=== API密钥管理系统测试 ===\n")
    
    # 创建测试用的管理器实例
    test_config = "test_api_keys.json"
    manager = APIKeyManager(test_config)
    
    # 清理之前的测试数据
    if os.path.exists(test_config):
        os.remove(test_config)
        manager = APIKeyManager(test_config)
    
    print("1. 初始状态检查")
    manager.show_status()
    
    print("\n2. 添加测试密钥")
    test_keys = [
        "sk-test1234567890abcdefghijklmnopqrstuvwxyz",
        "sk-demo9876543210zyxwvutsrqponmlkjihgfedcba",
        "api-key-example-111111111111111111111111111",
        "api-key-example-222222222222222222222222222",
        "api-key-example-333333333333333333333333333"
    ]
    
    added_count = manager.add_keys(test_keys)
    print(f"成功添加 {added_count} 个密钥")
    manager.show_status()
    
    print("\n3. 查看可用密钥")
    manager.list_available_keys(show_keys=False)
    
    print("\n4. 提取2个密钥")
    extracted = manager.extract_keys(2)
    print("提取的密钥：")
    for i, key in enumerate(extracted, 1):
        print(f"  {i}. {key}")
    
    print("\n5. 提取后状态")
    manager.show_status()
    
    print("\n6. 再次提取3个密钥")
    extracted2 = manager.extract_keys(3)
    print("提取的密钥：")
    for i, key in enumerate(extracted2, 1):
        print(f"  {i}. {key}")
    
    print("\n7. 最终状态")
    manager.show_status()
    
    print("\n8. 尝试提取超过可用数量的密钥")
    extracted3 = manager.extract_keys(10)
    print(f"尝试提取10个，实际提取了 {len(extracted3)} 个")
    
    print("\n9. 最终状态")
    manager.show_status()
    
    print("\n10. 清理测试文件")
    if os.path.exists(test_config):
        os.remove(test_config)
        print("测试文件已删除")
    
    print("\n=== 测试完成 ===")
    print("系统功能正常，可以开始使用！")

def demo_usage():
    """演示基本使用方法"""
    print("\n=== 使用演示 ===")
    print("1. 运行主程序：python api_key_manager.py")
    print("2. 选择选项2添加密钥")
    print("3. 选择选项1提取密钥")
    print("4. 输入要提取的数量")
    print("5. 获得密钥并使用")
    print("\n注意：提取后的密钥不能再次提取！")

if __name__ == "__main__":
    test_api_key_manager()
    demo_usage()
