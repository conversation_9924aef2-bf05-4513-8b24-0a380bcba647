#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API密钥管理系统 - 图形界面版本
基于tkinter的桌面应用程序
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import json
import os
from datetime import datetime
from api_key_manager import APIKeyManager

class APIKeyGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("API密钥提取管理系统")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 初始化管理器
        self.manager = APIKeyManager()
        
        # 设置样式
        self.setup_styles()
        
        # 创建界面
        self.create_widgets()
        
        # 更新状态显示
        self.update_status()
    
    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # 配置样式
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'))
        style.configure('Status.TLabel', font=('Arial', 10))
        style.configure('Action.TButton', font=('Arial', 10, 'bold'))
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="API密钥提取管理系统", style='Title.TLabel')
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 状态信息框架
        status_frame = ttk.LabelFrame(main_frame, text="系统状态", padding="10")
        status_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        status_frame.columnconfigure(1, weight=1)
        
        # 状态标签
        ttk.Label(status_frame, text="可用密钥:").grid(row=0, column=0, sticky=tk.W)
        self.available_label = ttk.Label(status_frame, text="0", style='Status.TLabel')
        self.available_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        ttk.Label(status_frame, text="已使用密钥:").grid(row=1, column=0, sticky=tk.W)
        self.used_label = ttk.Label(status_frame, text="0", style='Status.TLabel')
        self.used_label.grid(row=1, column=1, sticky=tk.W, padx=(10, 0))
        
        ttk.Label(status_frame, text="最后更新:").grid(row=2, column=0, sticky=tk.W)
        self.update_label = ttk.Label(status_frame, text="未知", style='Status.TLabel')
        self.update_label.grid(row=2, column=1, sticky=tk.W, padx=(10, 0))
        
        # 主要操作区域
        operation_frame = ttk.Frame(main_frame)
        operation_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        operation_frame.columnconfigure(0, weight=1)
        operation_frame.columnconfigure(1, weight=1)
        operation_frame.rowconfigure(1, weight=1)
        
        # 左侧：提取密钥
        extract_frame = ttk.LabelFrame(operation_frame, text="提取密钥", padding="10")
        extract_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        
        ttk.Label(extract_frame, text="提取数量:").grid(row=0, column=0, sticky=tk.W)
        self.extract_entry = ttk.Entry(extract_frame, width=10)
        self.extract_entry.grid(row=0, column=1, padx=(10, 0))
        
        extract_btn = ttk.Button(extract_frame, text="提取密钥", style='Action.TButton',
                                command=self.extract_keys)
        extract_btn.grid(row=1, column=0, columnspan=2, pady=(10, 0), sticky=(tk.W, tk.E))
        
        # 右侧：添加密钥
        add_frame = ttk.LabelFrame(operation_frame, text="添加密钥", padding="10")
        add_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))
        
        ttk.Label(add_frame, text="密钥列表 (每行一个):").grid(row=0, column=0, sticky=tk.W)
        
        self.add_text = scrolledtext.ScrolledText(add_frame, height=6, width=30)
        self.add_text.grid(row=1, column=0, pady=(5, 0), sticky=(tk.W, tk.E, tk.N, tk.S))
        add_frame.rowconfigure(1, weight=1)
        add_frame.columnconfigure(0, weight=1)
        
        add_btn = ttk.Button(add_frame, text="添加密钥", style='Action.TButton',
                            command=self.add_keys)
        add_btn.grid(row=2, column=0, pady=(10, 0), sticky=(tk.W, tk.E))
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(operation_frame, text="操作结果", padding="10")
        result_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        result_frame.rowconfigure(0, weight=1)
        result_frame.columnconfigure(0, weight=1)
        
        self.result_text = scrolledtext.ScrolledText(result_frame, height=10)
        self.result_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 底部按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=(10, 0))

        ttk.Button(button_frame, text="查看可用密钥", command=self.show_available_keys).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="清空已使用记录", command=self.clear_used_keys).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="刷新状态", command=self.update_status).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="清空结果", command=self.clear_result).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="导入文件", command=self.import_from_file).pack(side=tk.LEFT, padx=(5, 0))
    
    def update_status(self):
        """更新状态显示"""
        self.available_label.config(text=str(self.manager.get_available_count()))
        self.used_label.config(text=str(self.manager.get_used_count()))
        
        last_updated = self.manager.keys_data.get('last_updated', '未知')
        if last_updated != '未知':
            # 格式化时间显示
            try:
                dt = datetime.fromisoformat(last_updated.replace('Z', '+00:00'))
                formatted_time = dt.strftime('%Y-%m-%d %H:%M:%S')
                self.update_label.config(text=formatted_time)
            except:
                self.update_label.config(text=last_updated[:19])
        else:
            self.update_label.config(text=last_updated)
    
    def extract_keys(self):
        """提取密钥"""
        try:
            count_str = self.extract_entry.get().strip()
            if not count_str:
                messagebox.showwarning("输入错误", "请输入要提取的密钥数量")
                return
            
            count = int(count_str)
            if count <= 0:
                messagebox.showwarning("输入错误", "提取数量必须大于0")
                return
            
            # 检查可用密钥数量
            available = self.manager.get_available_count()
            if available == 0:
                messagebox.showinfo("提示", "没有可用的密钥")
                return
            
            # 提取密钥
            extracted = self.manager.extract_keys(count)
            
            if extracted:
                result = f"成功提取 {len(extracted)} 个密钥:\n\n"
                for i, key in enumerate(extracted, 1):
                    result += f"{i}. {key}\n"
                
                self.result_text.delete(1.0, tk.END)
                self.result_text.insert(tk.END, result)
                
                # 清空输入框
                self.extract_entry.delete(0, tk.END)
                
                # 更新状态
                self.update_status()
                
                messagebox.showinfo("成功", f"已提取 {len(extracted)} 个密钥")
            else:
                messagebox.showinfo("提示", "没有提取到密钥")
        
        except ValueError:
            messagebox.showerror("输入错误", "请输入有效的数字")
        except Exception as e:
            messagebox.showerror("错误", f"提取密钥时发生错误: {str(e)}")
    
    def add_keys(self):
        """添加密钥"""
        try:
            keys_text = self.add_text.get(1.0, tk.END).strip()
            if not keys_text:
                messagebox.showwarning("输入错误", "请输入要添加的密钥")
                return
            
            # 分割密钥
            keys = [key.strip() for key in keys_text.split('\n') if key.strip()]
            
            if not keys:
                messagebox.showwarning("输入错误", "没有找到有效的密钥")
                return
            
            # 添加密钥
            added_count = self.manager.add_keys(keys)
            
            result = f"尝试添加 {len(keys)} 个密钥\n"
            result += f"成功添加 {added_count} 个新密钥\n"
            if added_count < len(keys):
                result += f"跳过 {len(keys) - added_count} 个重复密钥\n"
            
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, result)
            
            # 清空输入框
            self.add_text.delete(1.0, tk.END)
            
            # 更新状态
            self.update_status()
            
            messagebox.showinfo("成功", f"成功添加 {added_count} 个密钥")
        
        except Exception as e:
            messagebox.showerror("错误", f"添加密钥时发生错误: {str(e)}")
    
    def show_available_keys(self):
        """显示可用密钥"""
        keys = self.manager.keys_data.get('keys', [])
        
        if not keys:
            result = "没有可用的密钥"
        else:
            result = f"可用密钥列表 ({len(keys)}个):\n\n"
            for i, key_info in enumerate(keys, 1):
                key_display = key_info['key'][:12] + "..." if len(key_info['key']) > 12 else key_info['key']
                added_time = key_info['added_at'][:19]
                result += f"{i}. {key_display} (添加时间: {added_time})\n"
        
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, result)
    
    def clear_used_keys(self):
        """清空已使用密钥记录"""
        if messagebox.askyesno("确认", "确定要清空所有已使用密钥的记录吗？"):
            try:
                cleared_count = self.manager.clear_used_keys()
                self.update_status()
                
                result = f"已清空 {cleared_count} 个已使用密钥的记录"
                self.result_text.delete(1.0, tk.END)
                self.result_text.insert(tk.END, result)
                
                messagebox.showinfo("成功", f"已清空 {cleared_count} 个记录")
            except Exception as e:
                messagebox.showerror("错误", f"清空记录时发生错误: {str(e)}")
    
    def clear_result(self):
        """清空结果显示"""
        self.result_text.delete(1.0, tk.END)

    def import_from_file(self):
        """从文件导入密钥"""
        from tkinter import filedialog

        file_path = filedialog.askopenfilename(
            title="选择密钥文件",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 过滤掉注释行和空行
                keys = []
                for line in content.split('\n'):
                    line = line.strip()
                    if line and not line.startswith('#'):
                        keys.append(line)

                if keys:
                    added_count = self.manager.add_keys(keys)

                    result = f"从文件导入:\n"
                    result += f"文件: {os.path.basename(file_path)}\n"
                    result += f"尝试导入 {len(keys)} 个密钥\n"
                    result += f"成功添加 {added_count} 个新密钥\n"

                    self.result_text.delete(1.0, tk.END)
                    self.result_text.insert(tk.END, result)

                    self.update_status()
                    messagebox.showinfo("成功", f"成功导入 {added_count} 个密钥")
                else:
                    messagebox.showwarning("警告", "文件中没有找到有效的密钥")

            except Exception as e:
                messagebox.showerror("错误", f"导入文件时发生错误: {str(e)}")


def main():
    """主函数"""
    root = tk.Tk()
    app = APIKeyGUI(root)
    
    # 设置窗口图标（如果有的话）
    try:
        # root.iconbitmap('icon.ico')  # 如果有图标文件
        pass
    except:
        pass
    
    # 启动主循环
    root.mainloop()


if __name__ == "__main__":
    main()
